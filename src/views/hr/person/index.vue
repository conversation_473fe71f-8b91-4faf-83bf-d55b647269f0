<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="姓名" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入姓名"
          clearable
          @keyup.enter="handleQuery"
          class="!w-140px"
        />
      </el-form-item>
      <el-form-item label="性别" prop="gender">
        <el-select v-model="queryParams.gender" placeholder="请选择" clearable class="!w-100px">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="身份证号" prop="idCard">
        <el-input
          v-model="queryParams.idCard"
          placeholder="请输入身份证号"
          clearable
          @keyup.enter="handleQuery"
          class="!w-180px"
        />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input
          v-model="queryParams.phone"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter="handleQuery"
          class="!w-130px"
        />
      </el-form-item>
      <el-form-item label="邮箱地址" prop="email">
        <el-input
          v-model="queryParams.email"
          placeholder="请输入邮箱地址"
          clearable
          @keyup.enter="handleQuery"
          class="!w-160px"
        />
      </el-form-item>
      <!--      <el-form-item label="城市" prop="city">-->
      <!--        <el-input v-model="queryParams.city" placeholder="请输入城市" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <el-form-item label="政治面貌" prop="politicalStatus">
        <el-select
          v-model="queryParams.politicalStatus"
          placeholder="请选择"
          clearable
          class="!w-100px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.POLITICAL_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="婚姻状况" prop="maritalStatus">-->
      <!--        <el-select v-model="queryParams.maritalStatus" placeholder="请选择" clearable class="!w-100px">-->
      <!--          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.MARITAL_STATUS)" :key="dict.value" :label="dict.label" :value="dict.value"/>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="毕业学校" prop="school">-->
      <!--        <el-input v-model="queryParams.school" placeholder="请输入毕业学校" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="最高学位" prop="degree">-->
      <!--        <el-input v-model="queryParams.degree" placeholder="请输入最高学位" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="最高学历" prop="educationLevel">-->
      <!--        <el-input v-model="queryParams.educationLevel" placeholder="请输入最高学历" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="公司名称" prop="companyName">-->
      <!--        <el-input v-model="queryParams.companyName" placeholder="请输入公司名称" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <el-form-item label="职位" prop="position">
        <el-input
          v-model="queryParams.position"
          placeholder="请输入职位"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="在职状态" prop="employmentStatus">
        <el-select
          v-model="queryParams.employmentStatus"
          placeholder="请选择"
          clearable
          class="!w-100px"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.EMPLOYMENT_STATUS)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <!--      <el-form-item label="工作年限" prop="workYear">-->
      <!--        <el-input v-model="queryParams.workYear" placeholder="请输入工作年限" clearable @keyup.enter="handleQuery" class="!w-240px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="参加工作时间" prop="workStartDate">-->
      <!--        <el-date-picker v-model="queryParams.workStartDate" value-format="YYYY-MM-DD HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="可入职时间" prop="availableDate">-->
      <!--        <el-date-picker v-model="queryParams.availableDate" value-format="YYYY-MM-DD HH:mm:ss" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-220px"/>-->
      <!--      </el-form-item>-->
      <!--      <el-form-item label="求职类型" prop="jobType">-->
      <!--        <el-select v-model="queryParams.jobType" placeholder="请选择求职类型" clearable class="!w-240px">-->
      <!--          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.JOB_TYPE)" :key="dict.value" :label="dict.label" :value="dict.value"/>-->
      <!--        </el-select>-->
      <!--      </el-form-item>-->
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" />
          搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置
        </el-button>
      </el-form-item>
      <br />
      <el-form-item>
        <el-button type="primary" @click="openResumeUpload" v-hasPermi="['hr:person:create']">
          <Icon icon="ep:upload-filled" class="mr-5px" />
          上传简历
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hr:person:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" />
          新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
          v-hasPermi="['hr:person:export']"
        >
          <Icon icon="ep:download" class="mr-5px" />
          导出
        </el-button>
        <el-button
          type="danger"
          plain
          :disabled="isEmpty(checkedIds)"
          @click="handleDeleteBatch"
          v-hasPermi="['hr:person:delete']"
        >
          <Icon icon="ep:delete" class="mr-5px" />
          批量删除
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      border
      row-key="id"
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      @selection-change="handleRowCheckboxChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="ID" align="center" prop="id" width="50" />
      <el-table-column label="姓名" align="center" prop="name" width="85" />
      <el-table-column label="性别" align="center" prop="gender" width="65">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_USER_SEX" :value="scope.row.gender || ''" />
        </template>
      </el-table-column>
      <el-table-column
        label="年龄"
        align="center"
        prop="age"
        :formatter="ageFormatter"
        width="65"
      />
      <!--      <el-table-column-->
      <!--        label="出生日期"-->
      <!--        align="center"-->
      <!--        prop="birthDate"-->
      <!--        :formatter="dateFormatter2"-->
      <!--        width="110"-->
      <!--      />-->
      <el-table-column label="身份证号" align="center" prop="idCard" width="165" />
      <el-table-column label="手机号码" align="center" prop="phone" width="115" />
      <!--      <el-table-column label="邮箱地址" align="center" prop="email"/>-->
      <!--      <el-table-column label="城市" align="center" prop="city" />-->
      <!--      <el-table-column label="现居住地址" align="center" prop="address"/>-->
      <!--      <el-table-column label="户籍地址" align="center" prop="registeredAddress"/>-->
      <!--      <el-table-column label="民族" align="center" prop="ethnicity">-->
      <!--        <template #default="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.ETHNICITY" :value="scope.row.ethnicity || ''"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="国籍" align="center" prop="nationality">-->
      <!--        <template #default="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.NATIONALITY" :value="scope.row.nationality || ''"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="政治面貌" align="center" prop="politicalStatus" width="100">-->
      <!--        <template #default="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.POLITICAL_STATUS" :value="scope.row.politicalStatus || ''"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="婚姻状况" align="center" prop="maritalStatus" width="100">-->
      <!--        <template #default="scope">-->
      <!--          <dict-tag :type="DICT_TYPE.MARITAL_STATUS" :value="scope.row.maritalStatus || ''"/>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
      <!--      <el-table-column label="毕业学校" align="center" prop="school" width="150"/>-->
      <el-table-column label="最高学历" align="center" prop="educationLevel" width="85">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EDUCATION_LEVEL" :value="scope.row.educationLevel || ''" />
        </template>
      </el-table-column>
      <el-table-column label="最高学位" align="center" prop="degree" width="85">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.DEGREE" :value="scope.row.degree || ''" />
        </template>
      </el-table-column>
      <el-table-column label="公司名称" align="center" prop="companyName" width="100" />
      <el-table-column label="职位" align="center" prop="position" width="100" />
      <el-table-column label="在职状态" align="center" prop="employmentStatus" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.EMPLOYMENT_STATUS" :value="scope.row.employmentStatus || ''" />
        </template>
      </el-table-column>
      <el-table-column label="工作年限" align="center" prop="workYear" width="100" />
      <el-table-column label="参加工作时间" align="center" prop="workStartDate" width="110" />
      <el-table-column label="可入职时间" align="center" prop="availableDate" width="110" />
      <el-table-column label="求职类型" align="center" prop="jobType" width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.JOB_TYPE" :value="scope.row.jobType || ''" />
        </template>
      </el-table-column>
      <!--      <el-table-column label="头像URL" align="center" prop="avatarUrl"/>-->
      <!--      <el-table-column-->
      <!--        label="创建时间"-->
      <!--        align="center"-->
      <!--        prop="createTime"-->
      <!--        :formatter="dateFormatter"-->
      <!--        width="180px"-->
      <!--      />-->
      <el-table-column label="简历文件" align="center" prop="resumeUrl" width="120" fixed="right">
        <template #default="{ row }">
          <div v-if="row.resumeUrl" class="flex justify-center items-center gap-2">
            <el-link
              type="primary"
              @click="previewResume(row.resumeUrl, row.name)"
              :underline="false"
            >
              预览
            </el-link>
            <el-dropdown trigger="click" @command="handleResumeCommand">
              <el-link type="info" :underline="false" class="text-xs">
                更多
                <el-icon class="el-icon--right">
                  <ArrowDown />
                </el-icon>
              </el-link>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item :command="{ action: 'download', row }">
                    <el-icon><Download /></el-icon>
                    下载
                  </el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'reupload', row }">
                    <el-icon><Upload /></el-icon>
                    重新上传
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" width="110px" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
            v-hasPermi="['hr:person:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
            v-hasPermi="['hr:person:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单抽屉：添加/修改 -->
  <PersonForm ref="formRef" @success="getList" />

  <!-- 简历上传弹窗 -->
  <ResumeUploadForm ref="resumeUploadRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { isEmpty } from '@/utils/is'
import download from '@/utils/download'
import { PersonApi, Person } from '@/api/hr/person'
import PersonForm from './PersonForm.vue'
import ResumeUploadForm from './ResumeUploadForm.vue'
import { openFilePreview } from '@/utils/filePreview'
import { ArrowDown, Download, Upload } from '@element-plus/icons-vue'
import { Icon } from '@/components/Icon'

/** 人员基本信息 列表 */
defineOptions({ name: 'Person' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<Person[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  gender: undefined,
  age: undefined,
  birthDate: [],
  idCard: undefined,
  phone: undefined,
  email: undefined,
  city: undefined,
  address: undefined,
  registeredAddress: undefined,
  ethnicity: undefined,
  nationality: undefined,
  politicalStatus: undefined,
  maritalStatus: undefined,
  school: undefined,
  degree: undefined,
  educationLevel: undefined,
  companyName: undefined,
  position: undefined,
  employmentStatus: undefined,
  workYear: undefined,
  workStartDate: [],
  availableDate: [],
  jobType: undefined,
  avatarUrl: undefined,
  resumeUrl: undefined,
  createTime: []
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await PersonApi.getPersonPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 简历上传操作 */
const resumeUploadRef = ref()
const openResumeUpload = () => {
  resumeUploadRef.value.open()
}

/** 预览简历文件 */
const previewResume = (resumeUrl: string, personName: string) => {
  if (!resumeUrl) {
    message.warning('该人员暂无简历文件')
    return
  }

  try {
    // 使用kkFileView预览简历文件
    openFilePreview(resumeUrl, `${personName}的简历`)
  } catch (error) {
    console.error('预览简历失败:', error)
    message.error('预览简历失败，请稍后重试')
  }
}

/** 下载简历文件 */
const downloadResume = (resumeUrl: string, personName: string) => {
  if (!resumeUrl) {
    message.warning('该人员暂无简历文件')
    return
  }

  try {
    // 获取文件扩展名
    const fileExtension = resumeUrl.split('.').pop() || 'pdf'
    const fileName = `${personName}_简历.${fileExtension}`

    // 创建下载链接
    const link = document.createElement('a')
    link.href = resumeUrl
    link.download = fileName
    link.target = '_blank'

    // 触发下载
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    message.success('简历下载已开始')
  } catch (error) {
    console.error('下载简历失败:', error)
    message.error('下载简历失败，请稍后重试')
  }
}

/** 年龄格式化器 */
const ageFormatter = (row: Person) => {
  if (!row.age || row.age <= 0) {
    return '-'
  }
  return row.age.toString()
}

/** 处理简历文件下拉菜单命令 */
const handleResumeCommand = (command: { action: string; row: Person }) => {
  const { action, row } = command

  switch (action) {
    case 'download':
      downloadResume(row.resumeUrl, row.name)
      break
    case 'reupload':
      reuploadResume(row)
      break
    default:
      break
  }
}

/** 重新上传简历 */
const reuploadResume = (person: Person) => {
  // 打开重新上传弹窗，传入人员信息
  resumeUploadRef.value.openForReupload(person)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await PersonApi.deletePerson(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 批量删除人员基本信息 */
const handleDeleteBatch = async () => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    await PersonApi.deletePersonList(checkedIds.value)
    message.success(t('common.delSuccess'))
    await getList()
  } catch {}
}

const checkedIds = ref<number[]>([])
const handleRowCheckboxChange = (records: Person[]) => {
  checkedIds.value = records.map((item) => item.id)
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await PersonApi.exportPerson(queryParams)
    download.excel(data, '人员基本信息.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
