# 简历上传和预览功能使用示例

## 功能演示

### 1. 启动kkFileView服务

```bash
# 使用Docker启动kkFileView服务
docker run -it -p 8012:8012 keking/kkfileview:4.1.0

# 或者下载jar包启动
java -jar kkFileView-4.1.0.jar
```

### 2. 配置环境变量

在项目根目录的环境变量文件中添加：

```bash
# .env.development 或 .env.production
VITE_KK_FILE_VIEW_URL=http://127.0.0.1:8012
```

### 3. 使用流程示例

#### 步骤1：上传简历

1. 访问人员管理页面：`/hr/person`
2. 点击"上传简历"按钮
3. 填写姓名：`张三`
4. 上传简历文件：`张三_简历.pdf`
5. 点击"确定"完成上传

#### 步骤2：预览和下载简历

1. 在人员列表中找到刚创建的记录
2. 在"简历文件"列中可以看到两个按钮：
   - **预览按钮**：点击在新窗口打开kkFileView预览页面
   - **下载按钮**：点击直接下载简历文件到本地
3. 下载的文件名格式为：`姓名_简历.扩展名`（如：`张三_简历.pdf`）

### 4. 技术实现细节

#### Base64编码过程

```javascript
// 原始文件URL
const fileUrl = 'http://example.com/files/resume.pdf'

// 使用crypto-js进行Base64编码
const encodedUrl = CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(fileUrl))

// 构建kkFileView预览URL
const previewUrl = `http://127.0.0.1:8012/onlinePreview?url=${encodeURIComponent(encodedUrl)}`
```

#### 预览URL示例

```
原始文件URL: http://localhost:8080/file/resume.pdf
Base64编码: aHR0cDovL2xvY2FsaG9zdDo4MDgwL2ZpbGUvcmVzdW1lLnBkZg==
最终预览URL: http://127.0.0.1:8012/onlinePreview?url=aHR0cDovL2xvY2FsaG9zdDo4MDgwL2ZpbGUvcmVzdW1lLnBkZg%3D%3D
```

#### 下载功能实现

```javascript
// 下载简历文件
const downloadResume = (resumeUrl, personName) => {
  // 获取文件扩展名
  const fileExtension = resumeUrl.split('.').pop() || 'pdf'
  const fileName = `${personName}_简历.${fileExtension}`

  // 创建下载链接
  const link = document.createElement('a')
  link.href = resumeUrl
  link.download = fileName
  link.target = '_blank'

  // 触发下载
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
```

#### 年龄计算逻辑

```javascript
// 年龄计算函数
const computedAge = computed(() => {
  if (!formData.value.birthDate) {
    return ''
  }
  const birthDate = new Date(formData.value.birthDate)
  const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  // 如果年龄小于等于0，显示横杠
  if (age <= 0) {
    return '-'
  }

  return age.toString()
})

// 年龄格式化器（用于列表显示）
const ageFormatter = (row) => {
  if (!row.age || row.age <= 0) {
    return '-'
  }
  return row.age.toString()
}
```

### 5. 错误处理示例

#### 场景1：kkFileView服务不可用

```javascript
// 自动降级处理
try {
  openFilePreview(fileUrl, fileName)
} catch (error) {
  // 降级为直接打开文件
  window.open(fileUrl, '_blank')
}
```

#### 场景2：文件不存在

```javascript
// 用户友好的错误提示
if (!resumeUrl) {
  message.warning('该人员暂无简历文件')
  return
}
```

### 6. 支持的文件格式

| 格式类型 | 支持的扩展名                         | 预览效果 |
| -------- | ------------------------------------ | -------- |
| 文档类   | PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX | 完整预览 |
| 文本类   | TXT, MD, XML, JSON, CSV              | 文本预览 |
| 图片类   | JPG, PNG, GIF, BMP, WEBP, SVG        | 图片预览 |
| 其他     | RTF, ODT, ODS, ODP                   | 基本预览 |

### 7. 性能优化建议

1. **文件大小控制**：建议简历文件不超过10MB
2. **缓存策略**：kkFileView会自动缓存转换后的文件
3. **网络优化**：确保文件存储服务与kkFileView在同一网络环境
4. **并发控制**：避免同时预览大量文件

### 8. 常见问题解决

#### 问题1：预览页面空白

- 检查kkFileView服务是否正常运行
- 检查文件URL是否可访问
- 检查Base64编码是否正确

#### 问题2：文件无法预览

- 确认文件格式是否支持
- 检查文件是否损坏
- 验证文件大小是否超限

#### 问题3：网络超时

- 增加kkFileView的超时配置
- 优化文件存储服务的响应速度
- 考虑使用CDN加速

### 9. 自定义配置

可以通过修改 `src/utils/filePreview.ts` 来自定义预览行为：

```typescript
// 自定义kkFileView服务地址
const KK_FILE_VIEW_URL = 'http://your-kkfileview-server:8012'

// 自定义支持的文件类型
const supportedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx']

// 自定义错误处理
export function openFilePreview(fileUrl: string, fileName?: string): void {
  // 添加自定义逻辑
}
```
