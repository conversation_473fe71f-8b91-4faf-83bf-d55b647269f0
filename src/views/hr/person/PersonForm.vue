<template>
  <el-drawer v-model="dialogVisible" size="1200px" direction="rtl" :destroy-on-close="true">
    <template #header>
      <div>
        <span>{{ dialogTitle }}</span>
        <el-divider class="!mt-2 !mb-0"/>
      </div>
    </template>
    <div class="flex flex-col h-full">
      <el-scrollbar class="flex-1">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="formRules"
          label-width="100px"
          v-loading="formLoading"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="姓名" prop="name">
                <el-input v-model="formData.name" placeholder="请输入姓名"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="性别" prop="gender">
                <el-radio-group v-model="formData.gender">
                  <el-radio-button
                    v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_USER_SEX)"
                    :key="dict.value"
                    :value="dict.value"
                    :label="dict.label"
                  />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="出生日期" prop="birthDate">
                <el-date-picker
                  v-model="formData.birthDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择出生日期"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="年龄" prop="age">
                <el-input v-model="computedAge" placeholder="根据生日自动计算" readonly/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="身份证号" prop="idCard">
                <el-input v-model="formData.idCard" placeholder="请输入身份证号"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号码" prop="phone">
                <el-input v-model="formData.phone" placeholder="请输入手机号码"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="邮箱地址" prop="email">
                <el-input v-model="formData.email" placeholder="请输入邮箱地址"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="城市" prop="city">
                <el-input v-model="formData.city" placeholder="请输入城市"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="现居住地址" prop="address">
                <el-input v-model="formData.address" placeholder="请输入现居住地址"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="户籍地址" prop="registeredAddress">
                <el-input v-model="formData.registeredAddress" placeholder="请输入户籍地址"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="民族" prop="ethnicity">
                <el-select v-model="formData.ethnicity" placeholder="请选择">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.ETHNICITY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="国籍" prop="nationality">
                <el-select v-model="formData.nationality" placeholder="请选择">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.NATIONALITY)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="政治面貌" prop="politicalStatus">
                <el-select v-model="formData.politicalStatus" placeholder="请选择">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.POLITICAL_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="婚姻状况" prop="maritalStatus">
                <el-radio-group v-model="formData.maritalStatus">
                  <el-radio-button
                    v-for="dict in getStrDictOptions(DICT_TYPE.MARITAL_STATUS)"
                    :key="dict.value"
                    :value="dict.value"
                    :label="dict.label"
                  />
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="毕业学校" prop="school">
                <el-input v-model="formData.school" placeholder="请输入毕业学校"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="最高学历" prop="educationLevel">
                <el-select v-model="formData.educationLevel" placeholder="请选择学历">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.EDUCATION_LEVEL)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="最高学位" prop="degree">
                <el-select v-model="formData.degree" placeholder="请选择学位">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.DEGREE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="公司名称" prop="companyName">
                <el-input v-model="formData.companyName" placeholder="请输入公司名称"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="职位" prop="position">
                <el-input v-model="formData.position" placeholder="请输入职位"/>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="在职状态" prop="employmentStatus">
                <el-select v-model="formData.employmentStatus" placeholder="请选择">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.EMPLOYMENT_STATUS)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="工作年限" prop="workYear">
                <el-input v-model="formData.workYear" placeholder="请输入工作年限"/>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="参加工作时间" prop="workStartDate">
                <el-date-picker
                  v-model="formData.workStartDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择参加工作时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="可入职时间" prop="availableDate">
                <el-date-picker
                  v-model="formData.availableDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="选择可入职时间"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="求职类型" prop="jobType">
                <el-select v-model="formData.jobType" placeholder="请选择">
                  <el-option
                    v-for="dict in getStrDictOptions(DICT_TYPE.JOB_TYPE)"
                    :key="dict.value"
                    :label="dict.label"
                    :value="dict.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!--      <el-form-item label="头像URL" prop="avatarUrl">-->
          <!--        <el-input v-model="formData.avatarUrl" placeholder="请输入头像URL" />-->
          <!--      </el-form-item>-->
          <!--      <el-form-item label="简历文件URL" prop="resumeUrl">-->
          <!--        <el-input v-model="formData.resumeUrl" placeholder="请输入简历文件URL" />-->
          <!--      </el-form-item>-->
        </el-form>
        <!-- 子表的表单 -->
        <el-tabs v-model="subTabsName">
          <el-tab-pane label="求职期望" name="personJobPref">
            <PersonJobPrefForm ref="personJobPrefFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="教育经历" name="personEducation">
            <PersonEducationForm ref="personEducationFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="工作经历" name="personWork">
            <PersonWorkForm ref="personWorkFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="项目经历" name="personProject">
            <PersonProjectForm ref="personProjectFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="培训经历" name="personTraining">
            <PersonTrainingForm ref="personTrainingFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="资格证书" name="personCertificate">
            <PersonCertificateForm ref="personCertificateFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="语言能力" name="personLanguage">
            <PersonLanguageForm ref="personLanguageFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
          <el-tab-pane label="技能特长" name="personSkill">
            <PersonSkillForm ref="personSkillFormRef" :person-id="formData.id || 0"/>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>
    </div>
    <template #footer>
      <div>
        <el-divider class="!mt-0 !mb-2"/>
        <div>
          <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import {getStrDictOptions, DICT_TYPE} from '@/utils/dict'
import {PersonApi, Person} from '@/api/hr/person'
import PersonCertificateForm from './components/PersonCertificateForm.vue'
import PersonEducationForm from './components/PersonEducationForm.vue'
import PersonJobPrefForm from './components/PersonJobPrefForm.vue'
import PersonLanguageForm from './components/PersonLanguageForm.vue'
import PersonProjectForm from './components/PersonProjectForm.vue'
import PersonSkillForm from './components/PersonSkillForm.vue'
import PersonTrainingForm from './components/PersonTrainingForm.vue'
import PersonWorkForm from './components/PersonWorkForm.vue'

/** 人员基本信息 表单 */
defineOptions({name: 'PersonForm'})

const {t} = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  gender: '1',
  age: undefined,
  birthDate: undefined,
  idCard: undefined,
  phone: undefined,
  email: undefined,
  city: undefined,
  address: undefined,
  registeredAddress: undefined,
  ethnicity: undefined,
  nationality: undefined,
  politicalStatus: undefined,
  maritalStatus: 'single',
  school: undefined,
  degree: undefined,
  educationLevel: undefined,
  companyName: undefined,
  position: undefined,
  employmentStatus: undefined,
  workYear: undefined,
  workStartDate: undefined,
  availableDate: undefined,
  jobType: undefined,
  avatarUrl: undefined,
  resumeUrl: undefined
})
const formRules = reactive({})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('personCertificate')
const personCertificateFormRef = ref()
const personEducationFormRef = ref()
const personJobPrefFormRef = ref()
const personLanguageFormRef = ref()
const personProjectFormRef = ref()
const personSkillFormRef = ref()
const personTrainingFormRef = ref()
const personWorkFormRef = ref()

/** 计算年龄 */
const computedAge = computed(() => {
  if (!formData.value.birthDate) {
    return ''
  }
  const birthDate = new Date(formData.value.birthDate)
  const today = new Date()
  let age = today.getFullYear() - birthDate.getFullYear()
  const monthDiff = today.getMonth() - birthDate.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--
  }

  // 如果年龄小于等于0，显示横杠
  if (age <= 0) {
    return '-'
  }

  return age.toString()
})

// 监听年龄变化，同步到formData
watch(computedAge, (newAge) => {
  // 如果是横杠或空值，设置为undefined；否则转换为数字
  ;(formData.value as any).age = newAge && newAge !== '-' ? parseInt(newAge) : undefined
})

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await PersonApi.getPerson(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({open}) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await personCertificateFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personCertificate'
    return
  }
  try {
    await personEducationFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personEducation'
    return
  }
  try {
    await personJobPrefFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personJobPref'
    return
  }
  try {
    await personLanguageFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personLanguage'
    return
  }
  try {
    await personProjectFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personProject'
    return
  }
  try {
    await personSkillFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personSkill'
    return
  }
  try {
    await personTrainingFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personTraining'
    return
  }
  try {
    await personWorkFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'personWork'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as Person
    // 拼接子表的数据
    data.personCertificates = personCertificateFormRef.value.getData()
    data.personEducations = personEducationFormRef.value.getData()
    data.personJobPrefs = personJobPrefFormRef.value.getData()
    data.personLanguages = personLanguageFormRef.value.getData()
    data.personProjects = personProjectFormRef.value.getData()
    data.personSkills = personSkillFormRef.value.getData()
    data.personTrainings = personTrainingFormRef.value.getData()
    data.personWorks = personWorkFormRef.value.getData()
    if (formType.value === 'create') {
      await PersonApi.createPerson(data)
      message.success(t('common.createSuccess'))
    } else {
      await PersonApi.updatePerson(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    gender: '1',
    age: undefined,
    birthDate: undefined,
    idCard: undefined,
    phone: undefined,
    email: undefined,
    city: undefined,
    address: undefined,
    registeredAddress: undefined,
    ethnicity: undefined,
    nationality: undefined,
    politicalStatus: undefined,
    maritalStatus: 'single',
    school: undefined,
    degree: undefined,
    educationLevel: undefined,
    companyName: undefined,
    position: undefined,
    employmentStatus: undefined,
    workYear: undefined,
    workStartDate: undefined,
    availableDate: undefined,
    jobType: undefined,
    avatarUrl: undefined,
    resumeUrl: undefined
  }
  formRef.value?.resetFields()
}
</script>

<style>
.el-drawer__header {
  margin-bottom: 8px !important;
  padding: 10px 20px !important;
  margin: 0 !important;
}

.el-drawer__footer {
  padding: 10px 20px !important;
}

.el-drawer__body {
  overflow-x: hidden !important;
  padding: 0 20px !important;
}
</style>
