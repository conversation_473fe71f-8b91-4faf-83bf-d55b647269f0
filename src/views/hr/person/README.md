# 人员管理 - 简历上传功能

## 功能说明

在人员管理页面中新增了简历上传功能，允许用户通过上传简历文件快速创建人员记录。

## 功能特点

1. **快速创建人员记录**：通过上传简历文件，系统会自动创建一个空白的人员数据库记录
2. **文件格式支持**：支持 PDF、DOC、DOCX 格式的简历文件
3. **文件存储**：上传的简历文件会存储到 `resumeUrl` 字段中
4. **基本信息录入**：用户需要输入姓名，其他字段可以后续编辑完善

## 使用方法

1. 在人员管理页面点击"上传简历"按钮
2. 在弹出的对话框中输入姓名
3. 拖拽或点击上传简历文件（支持 PDF、DOC、DOCX 格式）
4. 点击"确定"按钮完成上传

## 技术实现

### 组件结构

- `index.vue`：主页面，包含人员列表和操作按钮
- `ResumeUploadForm.vue`：简历上传弹窗组件

### 核心功能

1. **文件上传**：使用 `useUpload` hook 处理文件上传
2. **人员创建**：调用 `PersonApi.createPerson` 创建人员记录
3. **数据存储**：将上传的文件URL存储到 `resumeUrl` 字段

### 文件上传流程

1. 用户选择文件后，文件会上传到指定的 `resume` 目录
2. 上传成功后获取文件URL
3. 创建人员记录，将文件URL存储到 `resumeUrl` 字段
4. 刷新人员列表显示新创建的记录

### 数据结构

创建的人员记录包含以下字段：

- `name`：用户输入的姓名
- `resumeUrl`：上传的简历文件URL
- 其他字段：使用默认值或空值，可后续编辑

## 页面展示

### 简历文件列显示

在人员列表中新增了"简历文件"列，显示方式：

- 如果有简历文件：显示"查看简历"链接，点击可在新窗口打开
- 如果没有简历文件：显示"-"

### 操作按钮

在搜索栏下方新增"上传简历"按钮，点击后弹出上传对话框。

## 权限控制

上传简历功能需要 `hr:person:create` 权限。

## kkFileView 文件预览配置

### 环境变量配置

在项目的环境变量文件中添加kkFileView服务地址：

```bash
# kkFileView文件预览服务地址
VITE_KK_FILE_VIEW_URL=http://127.0.0.1:8012
```

### kkFileView 服务部署

1. **Docker 部署（推荐）**：

```bash
docker run -it -p 8012:8012 keking/kkfileview:4.1.0
```

2. **手动部署**：
   - 下载 kkFileView 发布包
   - 解压并运行 `bin/startup.sh`（Linux/Mac）或 `bin/startup.bat`（Windows）
   - 默认端口为 8012

### 预览功能特点

1. **文件格式支持**：

   - 文档类：PDF、DOC、DOCX、XLS、XLSX、PPT、PPTX
   - 文本类：TXT、MD、XML、JSON、CSV
   - 图片类：JPG、PNG、GIF、BMP、WEBP、SVG
   - 其他：RTF、ODT、ODS、ODP

2. **预览方式**：

   - 点击"预览简历"按钮在新窗口打开预览
   - 使用Base64编码确保URL安全传输
   - 支持文件名显示

3. **降级处理**：
   - 如果kkFileView服务不可用，自动降级为直接下载文件

## 注意事项

1. 文件大小限制：根据系统配置的文件上传限制
2. 文件格式：仅支持 PDF、DOC、DOCX 格式
3. 必填字段：姓名为必填字段
4. 文件存储：文件会存储到配置的文件存储服务中
5. **kkFileView服务**：需要确保kkFileView服务正常运行
6. **网络访问**：确保kkFileView服务能够访问到文件存储服务的URL
