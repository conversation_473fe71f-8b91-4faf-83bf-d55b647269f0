# 简历上传功能测试指南

## 测试环境准备

1. 确保项目正常运行
2. 确保有 `hr:person:create` 权限
3. 准备测试用的简历文件（PDF、DOC、DOCX格式）

## 测试步骤

### 1. 基本功能测试

1. 访问人员管理页面 `/hr/person`
2. 点击"上传简历"按钮
3. 验证弹窗是否正常打开
4. 输入姓名（必填）
5. 拖拽或点击上传简历文件
6. 点击"确定"按钮
7. 验证是否显示成功提示
8. 验证人员列表是否刷新并显示新记录
9. 验证"简历文件"列是否显示"预览"和"下载"按钮
10. 点击"预览"按钮，验证是否能正常打开kkFileView预览
11. 点击"下载"按钮，验证是否能正常下载文件到本地

### 2. 表单验证测试

1. 打开上传简历弹窗
2. 不输入姓名，直接点击"确定"
3. 验证是否显示"姓名不能为空"的错误提示
4. 输入姓名但不上传文件，点击"确定"
5. 验证是否显示"请上传简历文件"的错误提示

### 3. 文件格式测试

1. 尝试上传非支持格式的文件（如 .txt, .jpg 等）
2. 验证是否被正确拒绝
3. 上传支持格式的文件（PDF、DOC、DOCX）
4. 验证是否能正常上传

### 4. 文件数量限制测试

1. 尝试同时选择多个文件
2. 验证是否显示"最多只能上传一个简历文件！"的提示

### 5. 下载功能测试

1. **基本下载测试**：

   - 点击"下载"按钮
   - 验证是否开始下载文件
   - 检查下载的文件名格式是否为"姓名\_简历.扩展名"
   - 验证下载的文件内容是否完整

2. **文件名处理测试**：

   - 测试包含特殊字符的姓名
   - 验证文件名是否正确处理
   - 测试不同文件扩展名的处理

3. **错误处理测试**：
   - 测试无效的resumeUrl
   - 验证错误提示是否正确显示

### 6. 年龄计算测试

1. **正常年龄计算**：

   - 输入正常的出生日期（如：1990-01-01）
   - 验证年龄是否正确计算
   - 检查年龄字段是否自动更新

2. **负值年龄处理**：

   - 输入未来的出生日期（如：2030-01-01）
   - 验证年龄字段是否显示横杠"-"
   - 确认不会显示负数

3. **边界值测试**：

   - 输入今天的日期作为出生日期
   - 验证年龄是否显示横杠"-"
   - 输入昨天的日期，验证年龄是否为0并显示横杠"-"

4. **列表显示测试**：
   - 在人员列表中检查年龄列的显示
   - 验证负值或0值年龄是否显示为横杠"-"
   - 确认正常年龄正确显示数字

### 7. 取消操作测试

1. 打开上传简历弹窗
2. 输入一些信息
3. 点击"取消"按钮
4. 重新打开弹窗
5. 验证表单是否已重置

## 预期结果

### 成功场景

- 弹窗正常打开和关闭
- 表单验证正常工作
- 文件上传成功
- 人员记录创建成功
- 列表正常刷新
- 简历文件链接可以正常访问

### 错误处理

- 必填字段验证
- 文件格式验证
- 文件数量限制
- 网络错误处理

## 数据验证

### 数据库记录

创建的人员记录应包含：

- `name`: 用户输入的姓名
- `resumeUrl`: 上传文件的URL
- 其他字段: 空值或默认值

### 文件存储

- 文件应存储在 `resume` 目录下
- 文件URL应可正常访问
- 文件内容应完整无损

## 常见问题排查

### 1. 上传失败

- 检查文件大小是否超限
- 检查文件格式是否支持
- 检查网络连接
- 检查服务器存储配置

### 2. 权限问题

- 确认用户有 `hr:person:create` 权限
- 检查角色配置

### 3. 显示问题

- 检查组件是否正确导入
- 检查路由配置
- 检查样式是否正常加载

## 性能测试

1. 测试大文件上传（接近大小限制）
2. 测试并发上传
3. 测试网络较慢情况下的用户体验

## kkFileView 预览功能测试

### 1. 环境准备

1. 确保kkFileView服务正常运行（默认端口8012）
2. 配置环境变量 `VITE_KK_FILE_VIEW_URL`
3. 确保文件存储服务可被kkFileView访问

### 2. 预览功能测试

1. 上传简历文件后，在人员列表中点击"预览简历"
2. 验证是否在新窗口打开kkFileView预览页面
3. 验证文件内容是否正确显示
4. 测试不同格式文件的预览效果（PDF、DOC、DOCX）

### 3. 错误处理测试

1. **kkFileView服务不可用**：

   - 停止kkFileView服务
   - 点击预览按钮
   - 验证是否降级为直接打开文件URL

2. **文件URL无效**：
   - 修改数据库中的resumeUrl为无效地址
   - 点击预览按钮
   - 验证错误处理

### 4. Base64编码测试

1. 检查预览URL中的Base64编码是否正确
2. 手动解码验证原始文件URL
3. 测试包含特殊字符的文件URL

## 兼容性测试

1. 不同浏览器测试
2. 移动端适配测试
3. 不同文件格式兼容性测试
4. kkFileView版本兼容性测试
